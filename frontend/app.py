import streamlit as st
import requests
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import folium
from streamlit_folium import st_folium
from streamlit_option_menu import option_menu
from streamlit_autorefresh import st_autorefresh
import time

# Page configuration
st.set_page_config(
    page_title="Smart Vending Machine Console",
    page_icon="🏪",
    layout="wide",
    initial_sidebar_state="expanded"
)

# API Configuration
API_BASE_URL = "http://backend:8000"

# Custom CSS
st.markdown("""
<style>
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .alert-card {
        background-color: #fff3cd;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #ffc107;
    }
    .success-card {
        background-color: #d4edda;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #28a745;
    }
</style>
""", unsafe_allow_html=True)

# Helper functions
@st.cache_data(ttl=30)
def fetch_data(endpoint):
    try:
        response = requests.get(f"{API_BASE_URL}{endpoint}")
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"Error fetching data: {response.status_code}")
            return None
    except requests.exceptions.ConnectionError:
        st.error("Cannot connect to backend API. Please ensure the backend service is running.")
        return None

def main():
    st.title("🏪 Smart Vending Machine Console")
    
    # Auto-refresh every 30 seconds
    st_autorefresh(interval=30000, key="datarefresh")
    
    # Sidebar navigation
    with st.sidebar:
        selected = option_menu(
            "Navigation",
            ["Dashboard", "Machines", "Inventory", "Analytics", "Map View"],
            icons=["speedometer2", "cpu", "box", "graph-up", "geo-alt"],
            menu_icon="list",
            default_index=0,
        )
    
    if selected == "Dashboard":
        show_dashboard()
    elif selected == "Machines":
        show_machines()
    elif selected == "Inventory":
        show_inventory()
    elif selected == "Analytics":
        show_analytics()
    elif selected == "Map View":
        show_map_view()

def show_dashboard():
    st.header("📊 Dashboard Overview")
    
    # Fetch summary data
    summary = fetch_data("/analytics/summary")
    if not summary:
        return
    
    # Key metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="Total Machines",
            value=summary.get("total_machines", 0),
            delta=None
        )
    
    with col2:
        st.metric(
            label="Active Machines",
            value=summary.get("active_machines", 0),
            delta=None
        )
    
    with col3:
        st.metric(
            label="Total Sales",
            value=f"${summary.get('total_sales', 0):,.2f}",
            delta=None
        )
    
    with col4:
        st.metric(
            label="Low Stock Alerts",
            value=summary.get("low_stock_alerts", 0),
            delta=None
        )
    
    # Recent activity and alerts
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("🔔 Recent Alerts")
        if summary.get("low_stock_alerts", 0) > 0:
            st.warning(f"⚠️ {summary['low_stock_alerts']} items are running low on stock")
        else:
            st.success("✅ All inventory levels are healthy")
    
    with col2:
        st.subheader("📈 Quick Stats")
        st.info(f"💰 Total Transactions: {summary.get('total_transactions', 0):,}")
        
        # Calculate average per machine
        if summary.get("total_machines", 0) > 0:
            avg_sales = summary.get("total_sales", 0) / summary.get("total_machines", 1)
            st.info(f"📊 Average Sales per Machine: ${avg_sales:,.2f}")

def show_machines():
    st.header("🖥️ Machine Management")
    
    machines = fetch_data("/machines")
    if not machines:
        return
    
    # Machine status overview
    df_machines = pd.DataFrame(machines)
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("Machine List")
        
        # Display machines in a table
        for machine in machines:
            with st.expander(f"🏪 {machine['name']} - {machine['location']}"):
                col_a, col_b, col_c = st.columns(3)
                
                with col_a:
                    status_color = "🟢" if machine['status'] == 'active' else "🔴"
                    st.write(f"**Status:** {status_color} {machine['status'].title()}")
                    st.write(f"**Model:** {machine.get('model', 'N/A')}")
                
                with col_b:
                    st.write(f"**Capacity:** {machine.get('capacity', 'N/A')}")
                    st.write(f"**Installation:** {machine.get('installation_date', 'N/A')[:10]}")
                
                with col_c:
                    if st.button(f"View Details", key=f"details_{machine['id']}"):
                        show_machine_details(machine['id'])
    
    with col2:
        st.subheader("Status Distribution")
        if not df_machines.empty:
            status_counts = df_machines['status'].value_counts()
            fig = px.pie(
                values=status_counts.values,
                names=status_counts.index,
                title="Machine Status Distribution"
            )
            st.plotly_chart(fig, use_container_width=True)

def show_machine_details(machine_id):
    st.subheader(f"Machine {machine_id} Details")
    
    # Fetch machine analytics
    analytics = fetch_data(f"/analytics/machine/{machine_id}")
    if analytics:
        col1, col2 = st.columns(2)
        
        with col1:
            st.metric("Total Sales", f"${analytics['total_sales']:,.2f}")
            st.metric("Total Transactions", analytics['total_transactions'])
        
        with col2:
            if analytics['popular_products']:
                st.write("**Popular Products:**")
                for product in analytics['popular_products'][:3]:
                    st.write(f"• {product['name']}: {product['total_sold']} sold")

def show_inventory():
    st.header("📦 Inventory Management")
    
    machines = fetch_data("/machines")
    if not machines:
        return
    
    # Machine selector
    machine_names = {m['id']: f"{m['name']} - {m['location']}" for m in machines}
    selected_machine_id = st.selectbox(
        "Select Machine",
        options=list(machine_names.keys()),
        format_func=lambda x: machine_names[x]
    )
    
    if selected_machine_id:
        inventory = fetch_data(f"/machines/{selected_machine_id}/inventory")
        if inventory:
            df_inventory = pd.DataFrame(inventory)
            
            # Inventory overview
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("Current Stock Levels")
                for item in inventory:
                    product_name = item['product']['name']
                    current_stock = item['current_stock']
                    max_capacity = item['max_capacity']
                    min_threshold = item['min_threshold']
                    
                    # Stock level indicator
                    stock_percentage = (current_stock / max_capacity) * 100
                    
                    if current_stock <= min_threshold:
                        st.error(f"🔴 {product_name}: {current_stock}/{max_capacity} (LOW STOCK)")
                    elif stock_percentage < 50:
                        st.warning(f"🟡 {product_name}: {current_stock}/{max_capacity}")
                    else:
                        st.success(f"🟢 {product_name}: {current_stock}/{max_capacity}")
            
            with col2:
                st.subheader("Stock Level Chart")
                if not df_inventory.empty:
                    # Create stock level chart
                    stock_data = []
                    for item in inventory:
                        stock_data.append({
                            'Product': item['product']['name'],
                            'Current Stock': item['current_stock'],
                            'Max Capacity': item['max_capacity'],
                            'Stock %': (item['current_stock'] / item['max_capacity']) * 100
                        })
                    
                    df_stock = pd.DataFrame(stock_data)
                    fig = px.bar(
                        df_stock,
                        x='Product',
                        y='Stock %',
                        title="Stock Levels (%)",
                        color='Stock %',
                        color_continuous_scale='RdYlGn'
                    )
                    st.plotly_chart(fig, use_container_width=True)

def show_analytics():
    st.header("📈 Analytics & Reports")
    
    # Time period selector
    period = st.selectbox("Select Time Period", ["Last 7 days", "Last 30 days", "Last 90 days"])
    
    # Fetch data
    purchases = fetch_data("/purchases")
    machines = fetch_data("/machines")
    
    if not purchases or not machines:
        return
    
    df_purchases = pd.DataFrame(purchases)
    df_machines = pd.DataFrame(machines)
    
    if not df_purchases.empty:
        # Convert timestamp to datetime
        df_purchases['timestamp'] = pd.to_datetime(df_purchases['timestamp'])
        
        # Sales over time
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("Sales Over Time")
            daily_sales = df_purchases.groupby(df_purchases['timestamp'].dt.date)['total_amount'].sum()
            fig = px.line(
                x=daily_sales.index,
                y=daily_sales.values,
                title="Daily Sales",
                labels={'x': 'Date', 'y': 'Sales ($)'}
            )
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            st.subheader("Payment Methods")
            payment_counts = df_purchases['payment_method'].value_counts()
            fig = px.pie(
                values=payment_counts.values,
                names=payment_counts.index,
                title="Payment Method Distribution"
            )
            st.plotly_chart(fig, use_container_width=True)
        
        # Top performing machines
        st.subheader("Top Performing Machines")
        machine_sales = df_purchases.groupby('machine_id')['total_amount'].sum().sort_values(ascending=False)
        
        # Map machine IDs to names
        machine_map = {m['id']: m['name'] for m in machines}
        machine_sales.index = machine_sales.index.map(lambda x: machine_map.get(x, f"Machine {x}"))
        
        fig = px.bar(
            x=machine_sales.index,
            y=machine_sales.values,
            title="Sales by Machine",
            labels={'x': 'Machine', 'y': 'Total Sales ($)'}
        )
        st.plotly_chart(fig, use_container_width=True)

def show_map_view():
    st.header("🗺️ Machine Locations")
    
    machines = fetch_data("/machines")
    if not machines:
        return
    
    # Filter machines with coordinates
    machines_with_coords = [m for m in machines if m.get('latitude') and m.get('longitude')]
    
    if not machines_with_coords:
        st.warning("No machines have location coordinates set.")
        return
    
    # Create map
    if machines_with_coords:
        # Calculate center point
        avg_lat = sum(m['latitude'] for m in machines_with_coords) / len(machines_with_coords)
        avg_lon = sum(m['longitude'] for m in machines_with_coords) / len(machines_with_coords)
        
        m = folium.Map(location=[avg_lat, avg_lon], zoom_start=10)
        
        # Add markers for each machine
        for machine in machines_with_coords:
            # Determine marker color based on status
            color = 'green' if machine['status'] == 'active' else 'red'
            
            folium.Marker(
                [machine['latitude'], machine['longitude']],
                popup=f"""
                <b>{machine['name']}</b><br>
                Location: {machine['location']}<br>
                Status: {machine['status']}<br>
                Model: {machine.get('model', 'N/A')}
                """,
                tooltip=machine['name'],
                icon=folium.Icon(color=color)
            ).add_to(m)
        
        # Display map
        st_folium(m, width=700, height=500)
        
        # Machine status legend
        col1, col2 = st.columns(2)
        with col1:
            st.success("🟢 Active Machines")
        with col2:
            st.error("🔴 Inactive Machines")

if __name__ == "__main__":
    main()
