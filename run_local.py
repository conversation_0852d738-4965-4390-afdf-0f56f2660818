#!/usr/bin/env python3
"""
Local development setup for Smart Vending Machine Console
This script sets up a simple in-memory database and runs both services locally
"""

import subprocess
import sys
import time
import threading
import os
from pathlib import Path

def install_requirements():
    """Install required packages"""
    print("Installing backend requirements...")
    subprocess.run([sys.executable, "-m", "pip", "install", "-r", "backend/requirements.txt"], check=True)
    
    print("Installing frontend requirements...")
    subprocess.run([sys.executable, "-m", "pip", "install", "-r", "frontend/requirements.txt"], check=True)

def setup_local_database():
    """Create a simple SQLite database setup"""
    backend_dir = Path("backend")
    
    # Create a local database configuration
    local_db_config = '''
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os

# Use SQLite for local development
DATABASE_URL = "sqlite:///./vending_local.db"

engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
'''
    
    with open(backend_dir / "app" / "database_local.py", "w") as f:
        f.write(local_db_config)
    
    # Update main.py to use local database
    main_py_path = backend_dir / "app" / "main.py"
    with open(main_py_path, "r") as f:
        content = f.read()
    
    # Replace database import
    content = content.replace(
        "from app.database import get_db, engine",
        "from app.database_local import get_db, engine"
    )
    
    with open(main_py_path, "w") as f:
        f.write(content)

def run_backend():
    """Run the FastAPI backend"""
    os.chdir("backend")
    subprocess.run([sys.executable, "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"])

def run_frontend():
    """Run the Streamlit frontend"""
    # Update the API URL for local development first
    frontend_path = Path("frontend/app.py")
    if frontend_path.exists():
        with open(frontend_path, "r") as f:
            content = f.read()

        content = content.replace(
            'API_BASE_URL = "http://backend:8000"',
            'API_BASE_URL = "http://localhost:8000"'
        )

        with open(frontend_path, "w") as f:
            f.write(content)

    os.chdir("frontend")


    subprocess.run([sys.executable, "-m", "streamlit", "run", "app.py", "--server.port", "8501"])

def seed_database():
    """Seed the database with sample data"""
    print("Seeding database with sample data...")
    os.chdir("backend")
    subprocess.run([sys.executable, "seed_data.py"])
    os.chdir("..")

def main():
    print("🏪 Smart Vending Machine Console - Local Setup")
    print("=" * 50)
    
    try:
        # Install requirements
        install_requirements()
        
        # Setup local database
        setup_local_database()
        
        # Seed database
        seed_database()
        
        print("\n✅ Setup complete!")
        print("\nStarting services...")
        print("- Backend API will run on: http://localhost:8000")
        print("- Frontend Dashboard will run on: http://localhost:8501")
        print("\nPress Ctrl+C to stop both services")
        
        # Start backend in a separate thread
        backend_thread = threading.Thread(target=run_backend, daemon=True)
        backend_thread.start()
        
        # Wait a moment for backend to start
        time.sleep(3)
        
        # Start frontend (this will block)
        run_frontend()
        
    except KeyboardInterrupt:
        print("\n\n🛑 Shutting down services...")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
