# Smart Vending Machine Console

A comprehensive microservices-based console for managing smart vending machines with real-time monitoring, analytics, and observability.

## Features

- **Machine Management**: View and monitor multiple vending machines
- **Inventory Tracking**: Real-time stock levels and alerts
- **Purchase History**: Detailed transaction logs and analytics
- **Location-based Analytics**: Geographic distribution and performance
- **Interactive Dashboard**: Rich visualizations and statistics
- **Microservices Architecture**: Scalable and maintainable design

## Architecture

- **Frontend**: Streamlit dashboard with interactive visualizations
- **Backend**: FastAPI REST API with async support
- **Database**: PostgreSQL for data persistence
- **Containerization**: Docker with docker-compose orchestration

## Quick Start

### Option 1: Local Development (Recommended for testing)
1. Clone the repository
2. Run the local setup script:
   ```bash
   python run_local.py
   ```
3. Access the dashboard at `http://localhost:8501`
4. API documentation at `http://localhost:8000/docs`

### Option 2: Docker Compose (Production)
1. Clone the repository
2. Run with Docker Compose:
   ```bash
   docker-compose up --build
   ```
3. Access the dashboard at `http://localhost:8501`
4. API documentation at `http://localhost:8000/docs`

## Services

- **Frontend**: Streamlit app (port 8501)
- **Backend**: FastAPI server (port 8000)
- **Database**: PostgreSQL (port 5432)

## Development

See individual service directories for detailed setup instructions:
- `/frontend` - Streamlit dashboard
- `/backend` - FastAPI server
- `/database` - Database schemas and migrations
