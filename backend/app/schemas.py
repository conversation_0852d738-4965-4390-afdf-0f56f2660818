from pydantic import BaseModel
from datetime import datetime
from typing import List, Optional

# Product schemas
class ProductBase(BaseModel):
    name: str
    category: str
    price: float
    description: Optional[str] = None
    image_url: Optional[str] = None

class ProductCreate(ProductBase):
    pass

class Product(ProductBase):
    id: int
    
    class Config:
        from_attributes = True

# Machine schemas
class MachineBase(BaseModel):
    name: str
    location: str
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    status: str = "active"
    model: Optional[str] = None
    capacity: int = 100

class MachineCreate(MachineBase):
    pass

class Machine(MachineBase):
    id: int
    installation_date: datetime
    last_maintenance: Optional[datetime] = None
    
    class Config:
        from_attributes = True

# Inventory schemas
class InventoryItemBase(BaseModel):
    product_id: int
    current_stock: int = 0
    max_capacity: int = 10
    min_threshold: int = 2

class InventoryItemCreate(InventoryItemBase):
    pass

class InventoryItem(InventoryItemBase):
    id: int
    machine_id: int
    last_restocked: datetime
    product: Product
    
    class Config:
        from_attributes = True

# Purchase schemas
class PurchaseBase(BaseModel):
    machine_id: int
    product_id: int
    quantity: int = 1
    total_amount: float
    payment_method: str
    transaction_id: str
    success: bool = True

class PurchaseCreate(PurchaseBase):
    pass

class Purchase(PurchaseBase):
    id: int
    timestamp: datetime
    machine: Machine
    product: Product
    
    class Config:
        from_attributes = True

# Analytics schemas
class MachineAnalytics(BaseModel):
    machine_id: int
    total_sales: float
    total_transactions: int
    popular_products: List[dict]
    stock_alerts: List[dict]
    
class AnalyticsSummary(BaseModel):
    total_machines: int
    active_machines: int
    total_sales: float
    total_transactions: int
    low_stock_alerts: int
