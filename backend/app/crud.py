from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from app import models, schemas
from typing import List
from datetime import datetime, timedelta

# Machine CRUD operations
def get_machine(db: Session, machine_id: int):
    return db.query(models.Machine).filter(models.Machine.id == machine_id).first()

def get_machines(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.Machine).offset(skip).limit(limit).all()

def create_machine(db: Session, machine: schemas.MachineCreate):
    db_machine = models.Machine(**machine.dict())
    db.add(db_machine)
    db.commit()
    db.refresh(db_machine)
    return db_machine

# Product CRUD operations
def get_product(db: Session, product_id: int):
    return db.query(models.Product).filter(models.Product.id == product_id).first()

def get_products(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.Product).offset(skip).limit(limit).all()

def create_product(db: Session, product: schemas.ProductCreate):
    db_product = models.Product(**product.dict())
    db.add(db_product)
    db.commit()
    db.refresh(db_product)
    return db_product

# Inventory CRUD operations
def get_machine_inventory(db: Session, machine_id: int):
    return db.query(models.InventoryItem).filter(
        models.InventoryItem.machine_id == machine_id
    ).all()

def create_inventory_item(db: Session, item: schemas.InventoryItemCreate, machine_id: int):
    db_item = models.InventoryItem(**item.dict(), machine_id=machine_id)
    db.add(db_item)
    db.commit()
    db.refresh(db_item)
    return db_item

# Purchase CRUD operations
def get_purchases(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.Purchase).offset(skip).limit(limit).all()

def get_machine_purchases(db: Session, machine_id: int, skip: int = 0, limit: int = 100):
    return db.query(models.Purchase).filter(
        models.Purchase.machine_id == machine_id
    ).offset(skip).limit(limit).all()

def create_purchase(db: Session, purchase: schemas.PurchaseCreate):
    db_purchase = models.Purchase(**purchase.dict())
    db.add(db_purchase)
    
    # Update inventory
    inventory_item = db.query(models.InventoryItem).filter(
        models.InventoryItem.machine_id == purchase.machine_id,
        models.InventoryItem.product_id == purchase.product_id
    ).first()
    
    if inventory_item:
        inventory_item.current_stock -= purchase.quantity
    
    db.commit()
    db.refresh(db_purchase)
    return db_purchase

# Analytics functions
def get_analytics_summary(db: Session):
    total_machines = db.query(models.Machine).count()
    active_machines = db.query(models.Machine).filter(models.Machine.status == "active").count()
    
    total_sales = db.query(func.sum(models.Purchase.total_amount)).scalar() or 0
    total_transactions = db.query(models.Purchase).count()
    
    low_stock_alerts = db.query(models.InventoryItem).filter(
        models.InventoryItem.current_stock <= models.InventoryItem.min_threshold
    ).count()
    
    return {
        "total_machines": total_machines,
        "active_machines": active_machines,
        "total_sales": float(total_sales),
        "total_transactions": total_transactions,
        "low_stock_alerts": low_stock_alerts
    }

def get_machine_analytics(db: Session, machine_id: int):
    # Total sales for machine
    total_sales = db.query(func.sum(models.Purchase.total_amount)).filter(
        models.Purchase.machine_id == machine_id
    ).scalar() or 0
    
    # Total transactions
    total_transactions = db.query(models.Purchase).filter(
        models.Purchase.machine_id == machine_id
    ).count()
    
    # Popular products
    popular_products = db.query(
        models.Product.name,
        func.sum(models.Purchase.quantity).label('total_sold'),
        func.sum(models.Purchase.total_amount).label('revenue')
    ).join(models.Purchase).filter(
        models.Purchase.machine_id == machine_id
    ).group_by(models.Product.name).order_by(desc('total_sold')).limit(5).all()
    
    # Stock alerts
    stock_alerts = db.query(models.InventoryItem, models.Product).join(models.Product).filter(
        models.InventoryItem.machine_id == machine_id,
        models.InventoryItem.current_stock <= models.InventoryItem.min_threshold
    ).all()
    
    return {
        "machine_id": machine_id,
        "total_sales": float(total_sales),
        "total_transactions": total_transactions,
        "popular_products": [
            {"name": p.name, "total_sold": p.total_sold, "revenue": float(p.revenue)}
            for p in popular_products
        ],
        "stock_alerts": [
            {"product": alert.Product.name, "current_stock": alert.InventoryItem.current_stock}
            for alert in stock_alerts
        ]
    }
