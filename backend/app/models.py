from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Boolean, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base

class Machine(Base):
    __tablename__ = "machines"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    location = Column(String)
    latitude = Column(Float)
    longitude = Column(Float)
    status = Column(String, default="active")  # active, maintenance, offline
    model = Column(String)
    installation_date = Column(DateTime, server_default=func.now())
    last_maintenance = Column(DateTime)
    capacity = Column(Integer, default=100)
    
    # Relationships
    inventory_items = relationship("InventoryItem", back_populates="machine")
    purchases = relationship("Purchase", back_populates="machine")

class Product(Base):
    __tablename__ = "products"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    category = Column(String)
    price = Column(Float)
    description = Column(Text)
    image_url = Column(String)
    
    # Relationships
    inventory_items = relationship("InventoryItem", back_populates="product")
    purchases = relationship("Purchase", back_populates="product")

class InventoryItem(Base):
    __tablename__ = "inventory_items"

    id = Column(Integer, primary_key=True, index=True)
    machine_id = Column(Integer, ForeignKey("machines.id"))
    product_id = Column(Integer, ForeignKey("products.id"))
    current_stock = Column(Integer, default=0)
    max_capacity = Column(Integer, default=10)
    min_threshold = Column(Integer, default=2)
    last_restocked = Column(DateTime, server_default=func.now())
    
    # Relationships
    machine = relationship("Machine", back_populates="inventory_items")
    product = relationship("Product", back_populates="inventory_items")

class Purchase(Base):
    __tablename__ = "purchases"

    id = Column(Integer, primary_key=True, index=True)
    machine_id = Column(Integer, ForeignKey("machines.id"))
    product_id = Column(Integer, ForeignKey("products.id"))
    quantity = Column(Integer, default=1)
    total_amount = Column(Float)
    payment_method = Column(String)  # cash, card, mobile
    transaction_id = Column(String, unique=True, index=True)
    timestamp = Column(DateTime, server_default=func.now())
    success = Column(Boolean, default=True)
    
    # Relationships
    machine = relationship("Machine", back_populates="purchases")
    product = relationship("Product", back_populates="purchases")

class MaintenanceLog(Base):
    __tablename__ = "maintenance_logs"

    id = Column(Integer, primary_key=True, index=True)
    machine_id = Column(Integer, ForeignKey("machines.id"))
    maintenance_type = Column(String)  # routine, repair, emergency
    description = Column(Text)
    technician = Column(String)
    start_time = Column(DateTime, server_default=func.now())
    end_time = Column(DateTime)
    cost = Column(Float)
    status = Column(String, default="completed")  # scheduled, in_progress, completed
