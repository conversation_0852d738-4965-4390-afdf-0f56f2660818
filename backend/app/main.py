from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from typing import List
import uvicorn

from app.database_local import get_db, engine
from app import models, schemas, crud
from app.models import Base

# Create database tables
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="Smart Vending Machine API",
    description="API for managing smart vending machines, inventory, and analytics",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Smart Vending Machine API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

# Machine endpoints
@app.get("/machines", response_model=List[schemas.Machine])
def get_machines(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    machines = crud.get_machines(db, skip=skip, limit=limit)
    return machines

@app.get("/machines/{machine_id}", response_model=schemas.Machine)
def get_machine(machine_id: int, db: Session = Depends(get_db)):
    machine = crud.get_machine(db, machine_id=machine_id)
    if machine is None:
        raise HTTPException(status_code=404, detail="Machine not found")
    return machine

@app.post("/machines", response_model=schemas.Machine)
def create_machine(machine: schemas.MachineCreate, db: Session = Depends(get_db)):
    return crud.create_machine(db=db, machine=machine)

# Inventory endpoints
@app.get("/machines/{machine_id}/inventory", response_model=List[schemas.InventoryItem])
def get_machine_inventory(machine_id: int, db: Session = Depends(get_db)):
    return crud.get_machine_inventory(db, machine_id=machine_id)

@app.post("/machines/{machine_id}/inventory", response_model=schemas.InventoryItem)
def add_inventory_item(machine_id: int, item: schemas.InventoryItemCreate, db: Session = Depends(get_db)):
    return crud.create_inventory_item(db=db, item=item, machine_id=machine_id)

# Purchase endpoints
@app.get("/purchases", response_model=List[schemas.Purchase])
def get_purchases(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    return crud.get_purchases(db, skip=skip, limit=limit)

@app.get("/machines/{machine_id}/purchases", response_model=List[schemas.Purchase])
def get_machine_purchases(machine_id: int, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    return crud.get_machine_purchases(db, machine_id=machine_id, skip=skip, limit=limit)

@app.post("/purchases", response_model=schemas.Purchase)
def create_purchase(purchase: schemas.PurchaseCreate, db: Session = Depends(get_db)):
    return crud.create_purchase(db=db, purchase=purchase)

# Analytics endpoints
@app.get("/analytics/summary")
def get_analytics_summary(db: Session = Depends(get_db)):
    return crud.get_analytics_summary(db)

@app.get("/analytics/machine/{machine_id}")
def get_machine_analytics(machine_id: int, db: Session = Depends(get_db)):
    return crud.get_machine_analytics(db, machine_id=machine_id)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
