#!/usr/bin/env python3
"""
Script to seed the database with sample data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
try:
    from app.database_local import SessionLocal, engine
except ImportError:
    from app.database import SessionLocal, engine
from app import models, schemas
from datetime import datetime, timedelta
import random

def create_sample_data():
    # Create tables first
    models.Base.metadata.create_all(bind=engine)

    db = SessionLocal()

    try:
        # Check if data already exists
        existing_products = db.query(models.Product).first()
        if existing_products:
            print("Sample data already exists. Skipping...")
            return
        
        # Create sample products
        products_data = [
            {"name": "Coca Cola", "category": "Beverages", "price": 1.50, "description": "Classic Coca Cola 330ml"},
            {"name": "Pepsi", "category": "Beverages", "price": 1.50, "description": "Pepsi Cola 330ml"},
            {"name": "Water", "category": "Beverages", "price": 1.00, "description": "Pure drinking water 500ml"},
            {"name": "Snickers", "category": "Snacks", "price": 2.00, "description": "Snickers chocolate bar"},
            {"name": "Chips", "category": "Snacks", "price": 1.75, "description": "Potato chips 50g"},
            {"name": "Coffee", "category": "Beverages", "price": 2.50, "description": "Hot coffee"},
            {"name": "Sandwich", "category": "Food", "price": 4.50, "description": "Fresh sandwich"},
            {"name": "Energy Drink", "category": "Beverages", "price": 3.00, "description": "Energy drink 250ml"},
            {"name": "Cookies", "category": "Snacks", "price": 2.25, "description": "Chocolate chip cookies"},
            {"name": "Juice", "category": "Beverages", "price": 2.00, "description": "Orange juice 300ml"},
        ]
        
        products = []
        for product_data in products_data:
            product = models.Product(**product_data)
            db.add(product)
            products.append(product)
        
        db.commit()
        print(f"Created {len(products)} products")
        
        # Create sample machines
        machines_data = [
            {"name": "VM-001", "location": "Main Campus Building A", "latitude": 40.7128, "longitude": -74.0060, "status": "active", "model": "VendMax Pro", "capacity": 120},
            {"name": "VM-002", "location": "Student Center", "latitude": 40.7589, "longitude": -73.9851, "status": "active", "model": "VendMax Pro", "capacity": 120},
            {"name": "VM-003", "location": "Library Entrance", "latitude": 40.7505, "longitude": -73.9934, "status": "active", "model": "VendMax Lite", "capacity": 80},
            {"name": "VM-004", "location": "Cafeteria Hall", "latitude": 40.7614, "longitude": -73.9776, "status": "maintenance", "model": "VendMax Pro", "capacity": 120},
            {"name": "VM-005", "location": "Gym Lobby", "latitude": 40.7282, "longitude": -73.7949, "status": "active", "model": "VendMax Lite", "capacity": 80},
            {"name": "VM-006", "location": "Office Building B", "latitude": 40.7831, "longitude": -73.9712, "status": "active", "model": "VendMax Pro", "capacity": 120},
            {"name": "VM-007", "location": "Parking Garage", "latitude": 40.7549, "longitude": -73.9840, "status": "offline", "model": "VendMax Lite", "capacity": 80},
            {"name": "VM-008", "location": "Medical Center", "latitude": 40.7736, "longitude": -73.9566, "status": "active", "model": "VendMax Pro", "capacity": 120},
        ]
        
        machines = []
        for machine_data in machines_data:
            machine = models.Machine(**machine_data)
            db.add(machine)
            machines.append(machine)
        
        db.commit()
        print(f"Created {len(machines)} machines")
        
        # Create inventory items for active machines
        active_machines = [m for m in machines if m.status == "active"]
        for machine in active_machines:
            # Add 5-7 random products to each machine
            selected_products = random.sample(products, random.randint(5, 7))
            for product in selected_products:
                max_cap = random.randint(8, 15)
                current_stock = random.randint(1, max_cap)
                min_threshold = max(1, max_cap // 4)
                
                inventory_item = models.InventoryItem(
                    machine_id=machine.id,
                    product_id=product.id,
                    current_stock=current_stock,
                    max_capacity=max_cap,
                    min_threshold=min_threshold
                )
                db.add(inventory_item)
        
        db.commit()
        print("Created inventory items")
        
        # Create sample purchases
        payment_methods = ["cash", "card", "mobile"]
        for i in range(50):
            machine = random.choice(active_machines)
            product = random.choice(products)
            quantity = random.randint(1, 3)
            
            purchase = models.Purchase(
                machine_id=machine.id,
                product_id=product.id,
                quantity=quantity,
                total_amount=product.price * quantity,
                payment_method=random.choice(payment_methods),
                transaction_id=f"TXN{1000 + i}",
                timestamp=datetime.now() - timedelta(days=random.randint(0, 30)),
                success=random.choice([True, True, True, False])  # 75% success rate
            )
            db.add(purchase)
        
        db.commit()
        print("Created sample purchases")
        
        print("Sample data creation completed successfully!")
        
    except Exception as e:
        print(f"Error creating sample data: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_sample_data()
