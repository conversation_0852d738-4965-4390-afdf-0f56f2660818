-- Initialize database with sample data

-- Insert sample products
INSERT INTO products (name, category, price, description) VALUES
('Coca Cola', 'Beverages', 1.50, 'Classic Coca Cola 330ml'),
('Pepsi', 'Beverages', 1.50, 'Pepsi Cola 330ml'),
('Water', 'Beverages', 1.00, 'Pure drinking water 500ml'),
('Snickers', 'Snacks', 2.00, 'Snickers chocolate bar'),
('Chips', 'Snacks', 1.75, 'Potato chips 50g'),
('Coffee', 'Beverages', 2.50, 'Hot coffee'),
('Sandwich', 'Food', 4.50, 'Fresh sandwich'),
('Energy Drink', 'Beverages', 3.00, 'Energy drink 250ml'),
('Cookies', 'Snacks', 2.25, 'Chocolate chip cookies'),
('Juice', 'Beverages', 2.00, 'Orange juice 300ml');

-- Insert sample machines
INSERT INTO machines (name, location, latitude, longitude, status, model, capacity) VALUES
('VM-001', 'Main Campus Building A', 40.7128, -74.0060, 'active', 'VendMax Pro', 120),
('VM-002', 'Student Center', 40.7589, -73.9851, 'active', 'VendMax Pro', 120),
('VM-003', 'Library Entrance', 40.7505, -73.9934, 'active', 'VendMax Lite', 80),
('VM-004', 'Cafeteria Hall', 40.7614, -73.9776, 'maintenance', 'VendMax Pro', 120),
('VM-005', 'Gym Lobby', 40.7282, -73.7949, 'active', 'VendMax Lite', 80),
('VM-006', 'Office Building B', 40.7831, -73.9712, 'active', 'VendMax Pro', 120),
('VM-007', 'Parking Garage', 40.7549, -73.9840, 'offline', 'VendMax Lite', 80),
('VM-008', 'Medical Center', 40.7736, -73.9566, 'active', 'VendMax Pro', 120);

-- Insert sample inventory items
INSERT INTO inventory_items (machine_id, product_id, current_stock, max_capacity, min_threshold) VALUES
-- Machine 1
(1, 1, 8, 10, 2), (1, 2, 6, 10, 2), (1, 3, 12, 15, 3), (1, 4, 5, 8, 2), (1, 5, 7, 10, 2),
-- Machine 2  
(2, 1, 9, 10, 2), (2, 3, 14, 15, 3), (2, 6, 4, 8, 2), (2, 7, 3, 6, 1), (2, 8, 6, 10, 2),
-- Machine 3
(3, 1, 1, 10, 2), (3, 2, 8, 10, 2), (3, 4, 6, 8, 2), (3, 9, 4, 8, 2), (3, 10, 7, 10, 2),
-- Machine 5
(5, 3, 11, 15, 3), (5, 8, 8, 10, 2), (5, 6, 5, 8, 2), (5, 1, 9, 10, 2), (5, 5, 6, 10, 2),
-- Machine 6
(6, 1, 7, 10, 2), (6, 2, 5, 10, 2), (6, 3, 13, 15, 3), (6, 7, 2, 6, 1), (6, 9, 5, 8, 2),
-- Machine 8
(8, 1, 10, 10, 2), (8, 3, 15, 15, 3), (8, 6, 6, 8, 2), (8, 4, 7, 8, 2), (8, 10, 8, 10, 2);

-- Insert sample purchases (last 30 days)
INSERT INTO purchases (machine_id, product_id, quantity, total_amount, payment_method, transaction_id, timestamp, success) VALUES
(1, 1, 1, 1.50, 'card', 'TXN001', NOW() - INTERVAL '1 day', true),
(1, 4, 1, 2.00, 'cash', 'TXN002', NOW() - INTERVAL '1 day', true),
(2, 3, 2, 2.00, 'mobile', 'TXN003', NOW() - INTERVAL '2 days', true),
(3, 2, 1, 1.50, 'card', 'TXN004', NOW() - INTERVAL '2 days', true),
(1, 5, 1, 1.75, 'cash', 'TXN005', NOW() - INTERVAL '3 days', true),
(5, 8, 1, 3.00, 'card', 'TXN006', NOW() - INTERVAL '3 days', true),
(6, 1, 1, 1.50, 'mobile', 'TXN007', NOW() - INTERVAL '4 days', true),
(2, 6, 1, 2.50, 'card', 'TXN008', NOW() - INTERVAL '4 days', true),
(8, 3, 1, 1.00, 'cash', 'TXN009', NOW() - INTERVAL '5 days', true),
(1, 1, 2, 3.00, 'card', 'TXN010', NOW() - INTERVAL '5 days', true),
(3, 9, 1, 2.25, 'mobile', 'TXN011', NOW() - INTERVAL '6 days', true),
(5, 1, 1, 1.50, 'cash', 'TXN012', NOW() - INTERVAL '6 days', true),
(6, 7, 1, 4.50, 'card', 'TXN013', NOW() - INTERVAL '7 days', true),
(2, 8, 1, 3.00, 'mobile', 'TXN014', NOW() - INTERVAL '7 days', true),
(8, 4, 1, 2.00, 'card', 'TXN015', NOW() - INTERVAL '8 days', true),
(1, 3, 3, 3.00, 'cash', 'TXN016', NOW() - INTERVAL '8 days', true),
(5, 6, 1, 2.50, 'card', 'TXN017', NOW() - INTERVAL '9 days', true),
(3, 10, 1, 2.00, 'mobile', 'TXN018', NOW() - INTERVAL '9 days', true),
(6, 1, 1, 1.50, 'cash', 'TXN019', NOW() - INTERVAL '10 days', true),
(2, 3, 1, 1.00, 'card', 'TXN020', NOW() - INTERVAL '10 days', true);

-- Insert sample maintenance logs
INSERT INTO maintenance_logs (machine_id, maintenance_type, description, technician, start_time, cost, status) VALUES
(4, 'repair', 'Coin mechanism malfunction', 'John Smith', NOW() - INTERVAL '2 days', 150.00, 'completed'),
(7, 'emergency', 'Power supply failure', 'Jane Doe', NOW() - INTERVAL '5 days', 300.00, 'completed'),
(1, 'routine', 'Monthly maintenance check', 'Bob Johnson', NOW() - INTERVAL '7 days', 50.00, 'completed'),
(3, 'routine', 'Cleaning and restocking', 'Alice Brown', NOW() - INTERVAL '10 days', 25.00, 'completed');
